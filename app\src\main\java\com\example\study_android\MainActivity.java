package com.example.study_android;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.os.Build;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;

public class MainActivity extends AppCompatActivity {
    private static final int REQUEST_HISTORY = 1001;
    private static final int REQUEST_SETTINGS = 1002;
    
    private WebView webView;
    private EditText etUrl;
    private Button btnLoad, btnMenu, btnBack, btnForward, btnRefresh, btnHistory, btnSettings, btnToggleFullscreen;
    private LinearLayout toolbar, functionBar;
    
    private HistoryManager historyManager;
    private SettingsManager settingsManager;
    private String currentUrl = "https://www.baidu.com";
    private String currentTitle = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        initManagers();
        setupWebView();
        setupListeners();
        setupSystemUIListener();
        loadInitialPage();
        applySettings();
    }

    private void initViews() {
        webView = findViewById(R.id.webView);
        etUrl = findViewById(R.id.etUrl);
        btnLoad = findViewById(R.id.btnLoad);
        btnMenu = findViewById(R.id.btnMenu);
        btnBack = findViewById(R.id.btnBack);
        btnForward = findViewById(R.id.btnForward);
        btnRefresh = findViewById(R.id.btnRefresh);
        btnHistory = findViewById(R.id.btnHistory);
        btnSettings = findViewById(R.id.btnSettings);
        btnToggleFullscreen = findViewById(R.id.btnToggleFullscreen);
        toolbar = findViewById(R.id.toolbar);
        functionBar = findViewById(R.id.functionBar);
    }

    private void initManagers() {
        historyManager = new HistoryManager(this);
        settingsManager = new SettingsManager(this);
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        
        // 设置缓存
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        // setAppCacheEnabled() was deprecated in API 18 and removed in API 33
        // webSettings.setAppCacheEnabled(true);
        webSettings.setDatabaseEnabled(true);
        
        webView.setWebViewClient(new CustomWebViewClient(this, etUrl, btnBack, btnForward, historyManager));
        webView.setWebChromeClient(new CustomWebChromeClient(this));
    }

    private void setupSystemUIListener() {
        // 监听系统UI可见性变化，确保WebView布局正确适配
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
            getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> {
                // 当系统UI可见性改变时，延迟调整WebView布局
                webView.postDelayed(() -> {
                    adjustWebViewLayout();
                }, 200);
            });
        }
    }

    private void setupListeners() {
        btnLoad.setOnClickListener(v -> loadUrl(etUrl.getText().toString().trim()));
        
        btnMenu.setOnClickListener(v -> showMenu());
        
        btnBack.setOnClickListener(v -> {
            if (webView.canGoBack()) {
                webView.goBack();
            }
        });
        
        btnForward.setOnClickListener(v -> {
            if (webView.canGoForward()) {
                webView.goForward();
            }
        });
        
        btnRefresh.setOnClickListener(v -> webView.reload());
        
        btnHistory.setOnClickListener(v -> {
            Intent intent = new Intent(this, HistoryActivity.class);
            startActivityForResult(intent, REQUEST_HISTORY);
        });
        
        btnSettings.setOnClickListener(v -> {
            Intent intent = new Intent(this, SettingsActivity.class);
            startActivityForResult(intent, REQUEST_SETTINGS);
        });

        btnToggleFullscreen.setOnClickListener(v -> toggleFullscreenMode());
        
        etUrl.setOnEditorActionListener((v, actionId, event) -> {
            loadUrl(etUrl.getText().toString().trim());
            return true;
        });
    }

    private void loadInitialPage() {
        loadUrl(currentUrl);
    }

    private void loadUrl(String url) {
        if (url.isEmpty()) {
            Toast.makeText(this, "请输入网址", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 添加协议前缀
        if (!url.startsWith("http://") && !url.startsWith("https://")) {
            url = "https://" + url;
        }
        
        currentUrl = url;
        etUrl.setText(url);
        
        // 检查网络状态和离线模式
        if (settingsManager.isOfflineMode() || !isNetworkAvailable()) {
            webView.getSettings().setCacheMode(WebSettings.LOAD_CACHE_ONLY);
            Toast.makeText(this, "离线模式：加载缓存内容", Toast.LENGTH_SHORT).show();
        } else {
            if (settingsManager.isCacheEnabled()) {
                webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
            } else {
                webView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
            }
        }
        
        webView.loadUrl(url);
    }

    private void showMenu() {
        PopupMenu popup = new PopupMenu(this, btnMenu);
        popup.getMenuInflater().inflate(R.menu.main_menu, popup.getMenu());
        popup.setOnMenuItemClickListener(item -> {
            int id = item.getItemId();
            if (id == R.id.menu_home) {
                loadUrl("https://www.baidu.com");
                return true;
            } else if (id == R.id.menu_history) {
                Intent intent = new Intent(this, HistoryActivity.class);
                startActivityForResult(intent, REQUEST_HISTORY);
                return true;
            } else if (id == R.id.menu_settings) {
                Intent intent = new Intent(this, SettingsActivity.class);
                startActivityForResult(intent, REQUEST_SETTINGS);
                return true;
            } else if (id == R.id.menu_exit) {
                finish();
                return true;
            }
            return false;
        });
        popup.show();
    }

    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(CONNECTIVITY_SERVICE);
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        return activeNetworkInfo != null && activeNetworkInfo.isConnected();
    }

    private void applySettings() {
        // 应用全屏设置
        if (settingsManager.isFullscreenMode()) {
            // 隐藏状态栏和导航栏（现代全屏模式）
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_FULLSCREEN |
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION |
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY |
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                );
            } else {
                // 兼容旧版本
                getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
            }

            // 隐藏ActionBar（标题栏）
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
            // 隐藏自定义工具栏和功能栏
            toolbar.setVisibility(View.GONE);
            functionBar.setVisibility(View.GONE);
            // 显示悬浮按钮
            btnToggleFullscreen.setVisibility(View.VISIBLE);
            btnToggleFullscreen.setText("⛶"); // 退出全屏图标
        } else {
            // 显示状态栏和导航栏，确保内容不被遮挡
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                getWindow().getDecorView().setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                );
            } else {
                // 兼容旧版本
                getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
            }

            // 显示ActionBar（标题栏）
            if (getSupportActionBar() != null) {
                getSupportActionBar().show();
            }
            // 显示自定义工具栏和功能栏
            toolbar.setVisibility(View.VISIBLE);
            functionBar.setVisibility(View.VISIBLE);
            // 隐藏悬浮按钮
            btnToggleFullscreen.setVisibility(View.GONE);
        }

        // 应用缓存设置
        if (settingsManager.isCacheEnabled()) {
            webView.getSettings().setCacheMode(WebSettings.LOAD_DEFAULT);
        } else {
            webView.getSettings().setCacheMode(WebSettings.LOAD_NO_CACHE);
        }

        // 调整WebView布局以适配当前显示模式
        adjustWebViewLayout();
    }

    private void adjustWebViewLayout() {
        // 延迟执行以确保UI变化完成
        webView.postDelayed(() -> {
            // 强制重新布局以确保WebView正确适配新的显示模式
            webView.requestLayout();

            // 通知父布局重新计算
            if (webView.getParent() instanceof View) {
                ((View) webView.getParent()).requestLayout();
            }

            // 通知WebView视口大小可能已改变
            webView.evaluateJavascript("window.dispatchEvent(new Event('resize'));", null);

            // 确保WebView重新计算其内容大小
            webView.post(() -> {
                webView.computeScroll();
                webView.invalidate();
            });

        }, 150); // 延迟150ms以确保UI变化完全完成
    }

    private void toggleFullscreenMode() {
        // 切换全屏模式状态
        boolean currentFullscreenMode = settingsManager.isFullscreenMode();
        settingsManager.setFullscreenMode(!currentFullscreenMode);

        // 应用新的设置
        applySettings();

        // 调整WebView布局以适配新的显示模式
        adjustWebViewLayout();

        // 显示提示信息
        String message = settingsManager.isFullscreenMode() ? "已进入全屏模式" : "已退出全屏模式";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == REQUEST_HISTORY && resultCode == RESULT_OK && data != null) {
            String selectedUrl = data.getStringExtra("selected_url");
            if (selectedUrl != null) {
                loadUrl(selectedUrl);
            }
        } else if (requestCode == REQUEST_SETTINGS && resultCode == RESULT_OK) {
            applySettings();
        }
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 配置变化时（如屏幕旋转），重新调整WebView布局
        adjustWebViewLayout();
    }

    // Getter and setter methods for external classes
    public String getCurrentUrl() {
        return currentUrl;
    }

    public void setCurrentUrl(String url) {
        this.currentUrl = url;
    }

    public String getCurrentTitle() {
        return currentTitle;
    }

    public void setCurrentTitle(String title) {
        this.currentTitle = title;
    }
}
