[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\layout_item_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\layout\\item_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "com.example.study_android.app-merged_res-19:/drawable_floating_button_background.xml.flat", "source": "com.example.study_android.app-main-21:/drawable/floating_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-ldpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-ldpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\layout_activity_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\layout\\activity_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\layout_activity_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\layout\\activity_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\xml\\data_extraction_rules.xml"}, {"merged": "com.example.study_android.app-merged_res-19:/layout_activity_main.xml.flat", "source": "com.example.study_android.app-main-21:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\drawable-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-pngs-14:\\drawable-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-merged_res-19:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.5\\com.example.study_android.app-main-21:\\mipmap-hdpi\\ic_launcher_round.webp"}]