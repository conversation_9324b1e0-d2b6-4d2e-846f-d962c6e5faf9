<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部工具栏 -->
        <LinearLayout
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp"
            android:background="#f0f0f0">

            <!-- 地址栏 -->
            <EditText
                android:id="@+id/etUrl"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:hint="请输入网址"
                android:text="https://www.baidu.com"
                android:singleLine="true"
                android:background="@android:drawable/edit_text"
                android:paddingLeft="8dp"
                android:paddingRight="8dp" />

            <!-- 加载按钮 -->
            <Button
                android:id="@+id/btnLoad"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="加载"
                android:layout_marginLeft="8dp" />

            <!-- 菜单按钮 -->
            <Button
                android:id="@+id/btnMenu"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="菜单"
                android:layout_marginLeft="8dp" />

        </LinearLayout>

        <!-- 功能按钮栏 -->
        <LinearLayout
            android:id="@+id/functionBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="4dp"
            android:background="#e0e0e0">

            <Button
                android:id="@+id/btnBack"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="后退"
                android:textSize="12sp"
                android:layout_margin="2dp" />

            <Button
                android:id="@+id/btnForward"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="前进"
                android:textSize="12sp"
                android:layout_margin="2dp" />

            <Button
                android:id="@+id/btnRefresh"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="刷新"
                android:textSize="12sp"
                android:layout_margin="2dp" />

            <Button
                android:id="@+id/btnHistory"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="历史"
                android:textSize="12sp"
                android:layout_margin="2dp" />

            <Button
                android:id="@+id/btnSettings"
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:text="设置"
                android:textSize="12sp"
                android:layout_margin="2dp" />

        </LinearLayout>

        <!-- WebView -->
        <WebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

    <!-- 悬浮按钮 - 用于切换全屏模式 -->
    <Button
        android:id="@+id/btnToggleFullscreen"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/floating_button_background"
        android:text="⛶"
        android:textSize="18sp"
        android:textColor="#FFFFFF"
        android:elevation="8dp"
        android:visibility="gone" />

</RelativeLayout>
