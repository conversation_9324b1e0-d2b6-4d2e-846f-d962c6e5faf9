# 全屏模式悬浮按钮功能

## 功能描述
在Android浏览器应用中添加了全屏模式的悬浮按钮功能，用户可以通过悬浮按钮快速切换全屏和非全屏模式。

## 实现的功能
1. **悬浮按钮显示**：当应用进入全屏模式时，会在屏幕右下角显示一个圆形悬浮按钮
2. **快速切换**：点击悬浮按钮可以快速退出全屏模式，再次点击可以重新进入全屏模式
3. **状态提示**：切换模式时会显示Toast提示信息
4. **自动隐藏**：在非全屏模式下，悬浮按钮会自动隐藏
5. **完整全屏**：隐藏状态栏、导航栏、ActionBar（标题栏）和自定义工具栏，实现真正的全屏体验
6. **现代API支持**：使用现代的SystemUI API实现沉浸式全屏效果
7. **布局适配修复**：修复了非全屏模式下网页内容被标题栏遮挡的问题
8. **智能布局调整**：在全屏切换时自动调整WebView布局，确保内容正确显示
9. **默认无标题栏**：应用默认不显示ActionBar标题栏，提供更简洁的界面

## 技术实现

### 1. 布局文件修改 (activity_main.xml)
- 将原来的LinearLayout改为RelativeLayout以支持悬浮按钮的绝对定位
- 添加了悬浮按钮控件，设置在右下角位置
- 使用自定义的圆形背景drawable

### 2. MainActivity.java 修改
- 添加了btnToggleFullscreen按钮的引用
- 实现了toggleFullscreenMode()方法来切换全屏状态
- 修改了applySettings()方法来控制悬浮按钮的显示/隐藏和完整的全屏效果
- 添加了点击事件监听器
- 使用现代SystemUI API实现沉浸式全屏（API 19+）
- 隐藏ActionBar（标题栏）以实现完整全屏
- 添加了adjustWebViewLayout()方法来智能调整WebView布局
- 实现了SystemUI可见性变化监听器
- 添加了配置变化处理（如屏幕旋转）
- 修复了非全屏模式下内容被遮挡的问题

### 3. 样式文件
- 创建了floating_button_background.xml drawable资源
- 定义了圆形背景和边框样式

### 4. 主题修改 (themes.xml)
- 将应用主题从 `Theme.MaterialComponents.DayNight.DarkActionBar` 改为 `Theme.MaterialComponents.DayNight.NoActionBar`
- 默认隐藏ActionBar标题栏，提供更简洁的界面

## 使用方法
1. 通过设置页面开启全屏模式，或者
2. 在全屏模式下，点击右下角的悬浮按钮（⛶图标）即可退出全屏
3. 在非全屏模式下，可以通过设置页面重新进入全屏模式

## 文件修改清单
- `app/src/main/res/layout/activity_main.xml` - 添加悬浮按钮UI
- `app/src/main/java/com/example/study_android/MainActivity.java` - 实现悬浮按钮逻辑
- `app/src/main/res/drawable/floating_button_background.xml` - 悬浮按钮样式（新文件）
- `app/src/main/res/values/themes.xml` - 修改主题为NoActionBar，默认隐藏标题栏

## 注意事项
- 悬浮按钮只在全屏模式下显示
- 按钮位置固定在屏幕右下角，不会遮挡主要内容
- 切换全屏状态会同时更新SettingsManager中的设置
- 全屏模式会隐藏所有系统UI元素（状态栏、导航栏、标题栏）
- 使用沉浸式全屏模式，支持现代Android设备的全屏体验
- 兼容Android API 19+的现代全屏API，同时保持对旧版本的兼容性

## 技术细节
- 使用`SYSTEM_UI_FLAG_IMMERSIVE_STICKY`实现沉浸式全屏
- 自动隐藏系统UI，用户滑动边缘时临时显示
- 完全隐藏ActionBar和自定义工具栏
- 支持Android 4.4+的现代全屏API
- 智能布局调整：在UI状态变化时自动调整WebView布局
- 修复了`SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN`导致的内容遮挡问题
- 添加了系统UI可见性变化监听器，确保布局实时适配
- 支持配置变化（如屏幕旋转）时的布局自动调整
- 使用JavaScript事件通知网页视口大小变化

## 修复的问题
1. **内容遮挡问题**：修复了非全屏模式下网页内容被ActionBar和状态栏遮挡的问题
2. **布局适配问题**：确保在全屏和非全屏模式切换时，WebView能够正确适配新的布局
3. **视口大小问题**：通过JavaScript事件通知网页视口大小变化，确保响应式网页正确显示
4. **配置变化适配**：处理屏幕旋转等配置变化时的布局调整
